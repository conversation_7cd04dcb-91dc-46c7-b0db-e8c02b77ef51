// GET, gets State Resources
import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import { getActivityStateService, getActivitiesStateService } from '../../../../services/mssql/activity-state/get.service.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import getFile from '../../../../services/amqp/file/get.service.js'

const log = logger.create('HTTP-Controller.Get-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // state id means we want only 1 item
  const stateId = req.query.stateId?.toString()
  const activityId = req.query.activityId!.toString()
  const registration = req.query.registration?.toString()
  const since = req.query.since?.toString()
  const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))
  if (stateId) {
    try {
      const activityState = await getActivityStateService(stateId, agent.ID, activityId, registration)
      log('info', 'Successfully retrieved activity state', { id: activityState.fields.ID, success: true, req })
      if (activityState.fields.Etag) {
        res.setHeader('ETag', activityState.fields.Etag)
      }
      res.setHeader('Last-Modified', activityState.fields.ModifiedOn!.toISOString())
      res.setHeader('Content-Type', activityState.fields.ContentType!)

      if (activityState.fields.FileID) {
        // Retrieve file from FDS and send back to caller
        const file = await getFile(activityState.fields.FileID)
        res.set('Content-disposition', `filename=${file.filename}`)
        res.send(file.buffer.toString())
      } else {
        res.send(activityState.fields.State)
      }

    } catch (error) {
      const errorMessage = getErrorMessage(error)
      if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get activity state because was not found in the database', { stateId, errorMessage, success: false, req })
        res.status(NOT_FOUND).send('Activity state not found')
      } else {
        log('error', 'Failed to get activity state', { errorMessage, success: false, req })
        res.sendStatus(INTERNAL_SERVER_ERROR)
      }
    }
  } else {
    // No state id means we want an array of state ids
    try {
      const activitiesState = await getActivitiesStateService(activityId, registration, since)
      log('info', 'Successfully retrieved activities state', { req, activitiesStateIds: activitiesState.map(state=>state.fields.ID), success: true })
      res.json(activitiesState.map(state => state.fields.ID))

    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get activities state because was not found in the database', { stateId, success: false, req })
        res.status(NOT_FOUND).send('Activities state not found')
      } else {
        log('error', 'Failed to get activity states', { errorMessage: getErrorMessage(error), success: false, req })
        res.sendStatus(INTERNAL_SERVER_ERROR)
      }
    }
  }
}
