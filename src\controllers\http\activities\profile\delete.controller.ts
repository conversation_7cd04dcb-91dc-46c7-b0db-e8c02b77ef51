// DELETE, delete Profile Resources
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { getActivityProfileService, getActivitiesProfileService } from '../../../../services/mssql/activity-profile/get.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import deleteActivityProfile from '../../../../services/mssql/activity-profile/delete.service.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT } = httpStatus
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

const log = logger.create('HTTP-Controller.Delete-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // profile id means we want only 1 item to delete
    const profileId = req.query.profileId?.toString()
    const activityId = req.query.activityId!.toString()
    const since = req.query.since?.toString()

    // Single delete profileId is in params
    if (profileId) {
      try {
        const activityProfile = await getActivityProfileService(profileId, activityId)
        log('info', 'Successfully retrieved activity profile', { id: activityProfile.fields.ID, success: true })
        if (activityProfile.fields.Etag) {
          res.setHeader('ETag', activityProfile.fields.Etag)
        }
        res.setHeader('Last-Modified', activityProfile.fields.ModifiedOn!.toISOString())

        // delete record
        await deleteActivityProfile(activityProfile.fields.ID!, activityProfile.fields.ActivityID!)
        log('info', 'Successfully deleted activity profile', { id: activityProfile.fields.ID, success: true })

        // delete file if exists
        if (activityProfile.fields.FileID) {
          await deleteFile([activityProfile.fields.FileID])
        }
        res.status(NO_CONTENT)

      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          log('warn', 'Failed to delete activities profile because was not found in the database', { success: false, profileId })
          res.sendStatus(NO_CONTENT)
        } else {
          throw error
        }
      }
    }
    else {
      // Bulk delete profileId is not in params
      try {
        const activitiesProfile = await getActivitiesProfileService(activityId, since)
        log('info', 'Successfully retrieved activities profiles', { activityProfileIds: activitiesProfile.map(profile=>profile.fields.ID), success: true })

        await Promise.all(activitiesProfile.map(async activityProfile => {
          // delete record
          await deleteActivityProfile(activityProfile.fields.ID!, activityProfile.fields.ActivityID!)
          log('info', 'Successfully deleted activity profile', { id: activityProfile.fields.ID, success: true })

          // delete file if exists
          if (activityProfile.fields.FileID) {
            await deleteFile([activityProfile.fields.FileID])
          }
        }))
        res.status(NO_CONTENT)

      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          log('warn', 'Failed to delete activities profile because was not found in the database', { id: profileId, success: false })
          res.sendStatus(NO_CONTENT)
        } else {
          throw error
        }
      }
    }

  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to delete activity profile', { errorMessage, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
