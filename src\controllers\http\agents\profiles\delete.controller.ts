import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { getAgentProfileService, getAgentProfilesService } from '../../../../services/mssql/agent/profile/get.service.js'
import deleteAgentProfile from '../../../../services/mssql/agent/profile/delete.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT } = httpStatus
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'

const log = logger.create('HTTP-Controller.Delete-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const profileId = req.query.profileId?.toString()
    const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))

    const since = req.query.since?.toString()

    if (profileId) {
      try {
        const agentProfile = await getAgentProfileService(profileId, agent.ID)
        log('info', 'Successfully retrieved agent profile', { id: agentProfile.fields.ProfileID, success: true, req })
        if (agentProfile.fields.Etag) {
          res.setHeader('ETag', agentProfile.fields.Etag)
        }
        res.setHeader('Last-Updated', agentProfile.fields.Updated!.toISOString())

        // delete record
        await deleteAgentProfile(agent.ID, agentProfile.fields.ProfileID!)
        log('info', 'Successfully deleted agent profile', { id: agentProfile.fields.ProfileID, success: true, req })

        // delete file if exists
        if (agentProfile.fields.FileID) {
          await deleteFile([agentProfile.fields.FileID])
        }
        res.sendStatus(NO_CONTENT)

      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          log('warn', 'Failed to delete agent profile because was not found in the database', { id: profileId, success: false, req })
          res.status(NOT_FOUND).send('Agent Profile not found')
        } else {
          throw error
        }
      }
    } else {
      // Bulk delete profileId is not in params
      try {
        const agentsProfile = await getAgentProfilesService(agent.ID, since)
        log('info', 'Successfully retrieved agent profiles', { profileIds: agentsProfile.map(agent=>agent.fields.ProfileID), success: true, req })

        await Promise.all(agentsProfile.map(async agentProfile => {
          // delete record
          await deleteAgentProfile(agentProfile.fields.AgentID!, agentProfile.fields.ProfileID!)
          log('info', 'Successfully deleted agent profile', { id: agentProfile.fields.ProfileID, success: true })

          // delete file if exists
          if (agentProfile.fields.FileID) {
            await deleteFile([agentProfile.fields.FileID])
          }
        }))
        res.status(NO_CONTENT)

      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          log('warn', 'Failed to delete agent profile because was not found in the database', { agentId: agent.ID, success: false, req })
          res.status(NOT_FOUND).send('Activities profile not found')
        } else {
          throw error
        }
      }
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to delete agent profile', { errorMessage, success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
