// DELETE, delete State Resources
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { getActivityStateService, getActivitiesStateService } from '../../../../services/mssql/activity-state/get.service.js'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import deleteActivityState from '../../../../services/mssql/activity-state/delete.service.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT } = httpStatus
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

const log = logger.create('HTTP-Controller.Delete-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // state id means we want only 1 item to delete
    const stateId = req.query.stateId?.toString()
    const activityId = req.query.activityId!.toString()
    const registration = req.query.registration?.toString()
    const since = req.query.since?.toString()
    const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))

    // Single delete stateId is in params
    if (stateId) {
      try {
        const activityState = await getActivityStateService(stateId, agent.ID, activityId, registration)
        log('info', 'Successfully retrieved activity state', { id: activityState.fields.ID, success: true, req })
        if (activityState.fields.Etag) {
          res.setHeader('ETag', activityState.fields.Etag)
        }
        res.setHeader('Last-Modified', activityState.fields.ModifiedOn!.toISOString())

        // delete record
        await deleteActivityState(activityState.fields.ID!, activityState.fields.AgentID!, activityState.fields.ActivityID!, activityState.fields.RegistrationID)
        log('info', 'Successfully deleted activity state', { id: activityState.fields.ID, success: true, req })

        // delete file if exists
        if (activityState.fields.FileID) {
          await deleteFile([activityState.fields.FileID])
        }
        res.sendStatus(NO_CONTENT)

      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          log('warn', 'Failed to delete activities state because was not found in the database', { stateId, success: false, req })
          res.status(NOT_FOUND).send('Activities state not found')
        } else {
          throw error
        }
      }
    }
    else {
      // Bulk delete stateId is not in params
      try {
        const activitiesState = await getActivitiesStateService(activityId, registration, since)
        log('info', 'Successfully retrieved activities state', { activitiesStateIds: activitiesState.map(state=>state.fields.ID), req, success: true })

        await Promise.all(activitiesState.map(async activityState => {
          // delete record
          await deleteActivityState(activityState.fields.ID!, activityState.fields.AgentID!, activityState.fields.ActivityID!, activityState.fields.RegistrationID)
          log('info', 'Successfully deleted activity state', { id: activityState.fields.ID, success: true, req })

          // delete file if exists
          if (activityState.fields.FileID) {
            await deleteFile([activityState.fields.FileID])
          }
        }))
        res.sendStatus(NO_CONTENT)

      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          log('warn', 'Failed to delete activities state because was not found in the database', { stateId, success: false, req })
          res.status(NOT_FOUND).send('Activities state not found')
        } else {
          throw error
        }
      }
    }

  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to delete activity state', { errorMessage, success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
