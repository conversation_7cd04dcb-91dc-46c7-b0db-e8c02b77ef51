import { Table } from '@lcs/mssql-utility'
import { Statement, StatementTableName, StatementFields } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import Activity, { ActivityJSON } from './activity.model.js'
import Agent, { AgentJson } from './agents.model.js'
import CategoryContextActivity from './category-context-activities.model.js'
import GroupingContextActivity from './grouping-context-activity.model.js'
import OtherContextActivity from './other-context-activities.model.js'
import ParentContextActivity from './parent-context-activities.model.js'
import SubStatement, { SubStatementJson } from './sub-statement.model.js'
import VerbDisplay from './verb-display.model.js'
import { VerbJson } from './verb.model.js'
import getLangMap from '../utils/get-lang-map.js'
import StatementAttachment, { StatementAttachmentJson } from './attachment.model.js'

export interface StatementRef {
  objectType: string,
  id: string
}

export interface StatementJson {
  id?: string
  actor?: AgentJson
  verb?: VerbJson
  object?: ActivityJSON | AgentJson | StatementRef | SubStatementJson
  result?: {
    score?: {
      scaled?: number
      raw?: number
      min?: number
      max?: number
    },
    success?: boolean
    completion?: boolean
    response?: string
    duration?: string
    extensions?: { [key: string]: any }
  },
  context?: {
    registration?: string
    instructor?: AgentJson
    team?: AgentJson
    contextActivities?: {
      parent?: ActivityJSON[]
      grouping?: ActivityJSON[]
      category?: ActivityJSON[]
      other?: ActivityJSON[]
    },
    revision?: string
    platform?: string
    language?: string
    statement?: {
      objectType: string
      id: string
    }
    extensions?: { [key: string]: any }
  },
  timestamp?: string
  stored?: string
  authority?: AgentJson
  version?: string
  attachments?: StatementAttachmentJson[]
}

export default class StatementModel extends Table<StatementJson, Statement> {
  public fields!: StatementJson
  private readonly originalStatement: StatementJson

  public readonly actorID: string
  public readonly contextInstructorID?: string
  public readonly contextTeamID?: string
  public readonly objectAgentID?: string
  public readonly objectActivityID?: string
  public readonly objectSubStatementID?: string
  public readonly authorityID?: string

  private _objectAgent?: Agent
  private _objectActivity?: Activity
  private _objectSubStatement?: SubStatement
  private _actor?: Agent
  private _authority?: Agent
  public readonly voided: boolean = false
  private _contextInstructor?: Agent
  private _contextTeam?: Agent
  private _verbDisplay?: VerbDisplay[]
  private _objectStatementRefId?: string
  private _contextCategoryActivities?: Activity[]
  private _contextParentActivities?: Activity[]
  private _contextGroupingActivities?: Activity[]
  private _contextOtherActivities?: Activity[]
  private _statementAttachments?: StatementAttachment[]
  public get attachments(): StatementAttachment[] {
    return this._statementAttachments ?? []
  }

  constructor(fields?: StatementJson, record?: Statement) {
    super(StatementTableName, [
      StatementFields.ActorID,
      StatementFields.VerbID,
      StatementFields.Timestamp
    ])
    if (fields) {
      this.fields = fields
      this.fields.stored = (new Date()).toISOString()
      if (!this.fields.timestamp) {
        this.fields.timestamp = this.fields.stored
      }
      this.originalStatement = { ...fields } // make a copy before changes are made
      // set reference data
      this.actorID = ''
    } else if (record) {
      this.importFromDatabase(record)
      // Set reference data
      this.actorID = record.ActorID!
      this.contextInstructorID = record.ContextInstructorID
      this.contextTeamID = record.ContextTeamID
      this.objectActivityID = record.ObjectActivityID
      this.objectAgentID = record.ObjectAgentID
      this.objectSubStatementID = record.ObjectSubStatementID
      this.voided = record.Voided ?? false
      this.originalStatement = JSON.parse(record.Raw!)
      this.originalStatement.id = this.fields.id
      this.authorityID = record.AuthorityID
    } else {
      throw new Error('You must provide statement fields or database record')
    }
  }

  public importFromDatabase(record: Statement): void {
    this.fields = {
      id: record.ID?.toLowerCase(),
      stored: record.Stored!.toISOString(),
      timestamp: record.Timestamp!.toISOString(),
      version: record.Version !== null ? record.Version : undefined,
      verb: {
        id: record.VerbID!
      }
    }

    if (record.ResultCompletion !== null) {
      this.ensureResultField()
      this.fields.result!.completion = record.ResultCompletion
    }

    if (record.ResultDuration) {
      this.ensureResultField()
      this.fields.result!.duration = record.ResultDuration
    }

    if (record.ResultExtensions) {
      this.ensureResultField()
      this.fields.result!.extensions = JSON.parse(record.ResultExtensions)
    }

    if (record.ResultResponse) {
      this.ensureResultField()
      this.fields.result!.response = record.ResultResponse
    }

    if (record.ResultScoreMax !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.max = record.ResultScoreMax
    }

    if (record.ResultScoreMin !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.min = record.ResultScoreMin
    }

    if (record.ResultScoreRaw !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.raw = record.ResultScoreRaw
    }

    if (record.ResultScoreScaled !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.scaled = record.ResultScoreScaled
    }

    if (record.ResultSuccess !== null) {
      this.ensureResultField()
      this.fields.result!.success = record.ResultSuccess
    }

    if (record.ContextStatement) {
      this.ensureContextField()
      this.fields.context!.statement = {
        id: record.ContextStatement,
        objectType: 'StatementRef'
      }
    }

    if (record.ContextExtensions) {
      this.ensureContextField()
      this.fields.context!.extensions = JSON.parse(record.ContextExtensions)
    }

    if (record.ContextLanguage) {
      this.ensureContextField()
      this.fields.context!.language = record.ContextLanguage
    }

    if (record.ContextPlatform) {
      this.ensureContextField()
      this.fields.context!.platform = record.ContextPlatform
    }

    if (record.ContextRegistration) {
      this.ensureContextField()
      this.fields.context!.registration = record.ContextRegistration
    }

    if (record.ContextRevision) {
      this.ensureContextField()
      this.fields.context!.revision = record.ContextRevision
    }

    if (record.ObjectStatementRef) {
      this.fields.object = {
        objectType: 'StatementRef',
        id: record.ObjectStatementRef
      }
      this._objectStatementRefId = record.ObjectStatementRef
    }
  }

  public exportJsonToDatabase(): Statement {
    return {
      ID: this.fields.id,
      ObjectAgentID: this._objectAgent ? this._objectAgent.ID : undefined,
      ObjectActivityID: this._objectActivity ? this._objectActivity.fields.id : undefined,
      ObjectSubStatementID: this._objectSubStatement ? this._objectSubStatement.ID : undefined,
      ObjectStatementRef: this.fields.object && this.fields.object.objectType === 'StatementRef' && 'id' in this.fields.object ? this.fields.object.id : undefined,
      ActorID: this._actor!.ID,
      VerbID: this.fields.verb!.id,
      ResultSuccess: this.fields.result?.success,
      ResultCompletion: this.fields.result?.completion,
      ResultResponse: this.fields.result?.response,
      ResultDuration: this.fields.result?.duration,
      ResultScoreScaled: this.fields.result?.score?.scaled,
      ResultScoreMax: this.fields.result?.score?.max,
      ResultScoreMin: this.fields.result?.score?.min,
      ResultScoreRaw: this.fields.result?.score?.raw,
      ResultExtensions: this.fields.result?.extensions ? JSON.stringify(this.fields.result.extensions) : undefined,
      Stored: this.fields.stored && !isNaN(Date.parse(this.fields.stored)) ? new Date(this.fields.stored) : new Date(),
      Timestamp: this.fields.timestamp && !isNaN(Date.parse(this.fields.timestamp)) ? new Date(this.fields.timestamp) : new Date(),
      AuthorityID: this._authority ? this._authority.ID : undefined,
      Voided: this.voided,
      ContextRegistration: this.fields.context?.registration,
      ContextInstructorID: this._contextInstructor ? this._contextInstructor.ID : undefined,
      ContextTeamID: this._contextTeam ? this._contextTeam.ID : undefined,
      ContextRevision: this.fields.context?.revision,
      ContextPlatform: this.fields.context?.platform,
      ContextLanguage: this.fields.context?.language,
      ContextExtensions: this.fields.context?.extensions ? JSON.stringify(this.fields.context.extensions) : undefined,
      ContextStatement: this.fields.context?.statement?.id,
      Version: this.fields.version,
      Raw: JSON.stringify(this.originalStatement)
    }
  }

  public attachObjectAgent(agent: Agent) {
    this._objectAgent = agent
    this.fields.object = agent.fields
  }

  public attachObjectActivity(activity: Activity) {
    this._objectActivity = activity
    this.fields.object = activity.fields
  }

  public attachObjectSubStatement(subStatement: SubStatement) {
    this._objectSubStatement = subStatement
    this.fields.object = subStatement.fields
  }

  public attachActor(actor: Agent) {
    this._actor = actor
    this.fields.actor = actor.fields
  }

  public attachAuthority(authority: Agent) {
    this._authority = authority
    this.fields.authority = authority.fields
  }

  public attachContextInstructor(instructor: Agent) {
    this._contextInstructor = instructor
    if (!this.fields.context) {
      this.fields.context = {
        instructor: instructor.fields
      }
    } else {
      this.fields.context.instructor = instructor.fields
    }
  }

  public attachContextTeam(team: Agent) {
    this._contextTeam = team
    if (!this.fields.context) {
      this.fields.context = {
        team: team.fields
      }
    } else {
      this.fields.context.team = team.fields
    }
  }

  public attachContextParentActivities(activities: Activity[]) {
    this._contextParentActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.parent = activities.map(activity => activity.fields)
  }

  public getContextParentJoins(): ParentContextActivity[] {
    if (this.fields.context?.contextActivities?.parent) {
      return this.fields.context.contextActivities.parent.map(activity => new ParentContextActivity({
        StatementID: this.fields.id!,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public attachContextOtherActivities(activities: Activity[]) {
    this._contextOtherActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.other = activities.map(activity => activity.fields)
  }

  public getContextOtherJoins(): OtherContextActivity[] {
    if (this.fields.context?.contextActivities?.other) {
      return this.fields.context.contextActivities.other.map(activity => new OtherContextActivity({
        StatementID: this.fields.id!,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public attachContextGroupingActivities(activities: Activity[]) {
    this._contextGroupingActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.grouping = activities.map(activity => activity.fields)
  }

  public getContextGroupingJoins(): GroupingContextActivity[] {
    if (this.fields.context?.contextActivities?.grouping) {
      return this.fields.context.contextActivities.grouping.map(activity => new GroupingContextActivity({
        StatementID: this.fields.id!,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public attachContextCategoryActivities(activities: Activity[]) {
    this._contextCategoryActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.category = activities.map(activity => activity.fields)
  }

  public getContextCategoryJoins(): CategoryContextActivity[] {
    if (this.fields.context?.contextActivities?.category) {
      return this.fields.context.contextActivities.category.map(activity => new CategoryContextActivity({
        StatementID: this.fields.id!,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public getStatementAttachmentMetadataJoins(): StatementAttachment[] {
    if (this.fields.attachments && this.fields.attachments.length > 0) {
      return this.fields.attachments.map(attachment => new StatementAttachment({
        UsageType: attachment.usageType,
        ContentType: attachment.contentType,
        Length: attachment.length,
        Sha2: attachment.sha2,
        FileUrl: attachment.fileUrl,
        StatementId: this.fields.id,
        Display: attachment.display ? JSON.stringify(attachment.display) : undefined,
        Description: attachment.description ? JSON.stringify(attachment.description) : undefined
      }))
    }
    return []
  }

  public attachAttachmentMetadata(attachments: StatementAttachment[]) {
    this._statementAttachments = attachments
  }

  private ensureContextActivitiesFieldExists() {
    this.ensureContextField()
    if (!this.fields.context!.contextActivities) {
      this.fields.context!.contextActivities = {}
    }
  }

  public attachVerbName(name: VerbDisplay[]) {
    this._verbDisplay = name
    if (name.length <= 0) {
      return
    }
    if (!this.fields.verb) {
      this.fields.verb = {
        id: name[0].fields.VerbID!
      }
    }
    this.fields.verb.display = name.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  private ensureResultField(ensureScore = false): void {
    if (!this.fields.result) {
      this.fields.result = {}
    }
    if (ensureScore && !this.fields.result.score) {
      this.fields.result.score = {}
    }
  }

  private ensureContextField(): void {
    if (!this.fields.context) {
      this.fields.context = {}
    }
  }

  public toJson(language?: string[], format = 'exact'): { [key: string]: any } {
    // build response object
    if (format === 'exact') {
      return this.originalStatement
    }

    // format is not exact, either it's ids or canonical
    // build the response
    const ret: { [key: string]: any } = {}

    // are we looking for ids only?
    const idsOnly = format === 'ids'

    // add the statement id
    ret.id = this.fields.id

    // attach the actor
    ret.actor = this._actor?.toJson(idsOnly)
    // attach the verb
    ret.verb = {
      id: this.fields.verb?.id
    }

    ret.authority = this._authority?.toJson(idsOnly)

    if (!idsOnly) {
      if (this._verbDisplay) {
        ret.verb.display = getLangMap(this._verbDisplay.map(display => { return { Lang: display.fields.Lang!, Display: display.fields.Display! } }), language)
      } else {
        ret.verb.display = {}
      }
    }

    if (this._objectAgent) {
      ret.object = this._objectAgent.toJson(idsOnly)
    } else if (this._objectActivity) {
      ret.object = this._objectActivity.toJson(language, idsOnly)
    } else if (this._objectSubStatement) {
      ret.object = this._objectSubStatement.toJson(language, idsOnly)
    } else {
      ret.object = {
        id: this._objectStatementRefId,
        objectType: 'StatementRef'
      }
    }

    if (this.fields.result) {
      ret.result = this.fields.result
    }

    if (this.fields.context) {
      this.buildContextReturn(ret, idsOnly, language)
    }

    ret.timestamp = this.fields.timestamp
    ret.stored = this.fields.stored
    ret.version = this.fields.version

    if (this._authority) {
      ret.authority = this._authority.toJson(idsOnly)
    }

    if (this._statementAttachments && this._statementAttachments.length > 0) {
      ret.attachments = this._statementAttachments.map(attachment => {
        const attachJson: { [key: string]: any } = {
          usageType: attachment.fields.UsageType,
          contentType: attachment.fields.ContentType,
          length: attachment.fields.Length,
          sha2: attachment.fields.Sha2
        }

        if (attachment.fields.Description) {
          const description = JSON.parse(attachment.fields.Description)
          const descriptionLangMap: { Lang: string, Display: string }[] = []
          for (const key in description) {
            descriptionLangMap.push({
              Lang: key,
              Display: description[key]
            })
          }
          attachJson.description = getLangMap(descriptionLangMap, language)
        }

        if (attachment.fields.Display) {
          const display = JSON.parse(attachment.fields.Display)
          const displayLangMap: { Lang: string, Display: string }[] = []
          for (const key in display) {
            displayLangMap.push({
              Lang: key,
              Display: display[key]
            })
          }
          attachJson.display = getLangMap(displayLangMap, language)
        }

        if (attachment.fields.FileUrl) {
          attachJson.fileUrl = attachment.fields.FileUrl
        }

        return attachJson
      })
    }

    return ret
  }

  private buildContextReturn(ret: any, idsOnly: boolean, language?: string[]): void {
    ret.context = {}
    if (this.fields.context?.contextActivities) {
      this.buildContextActivities(ret, idsOnly, language)
    }
    if (this.fields.context?.extensions) {
      ret.context.extensions = this.fields.context?.extensions
    }
    if (this._contextInstructor) {
      ret.context.instructor = this._contextInstructor.toJson(idsOnly)
    }
    if (this._contextTeam) {
      ret.context.team = this._contextTeam.toJson(idsOnly)
    }
    if (this.fields.context?.registration) {
      ret.context.registration = this.fields.context?.registration
    }
    if (this.fields.context?.revision) {
      ret.context.revision = this.fields.context?.revision
    }
    if (this.fields.context?.platform) {
      ret.context.platform = this.fields.context?.platform
    }
    if (this.fields.context?.language) {
      ret.context.language = this.fields.context?.language
    }
    if (this.fields.context?.statement) {
      ret.context.statement = this.fields.context?.statement
    }
  }

  private buildContextActivities(ret: any, idsOnly: boolean, language?: string[]): void {
    ret.context.contextActivities = {}
    if (this._contextCategoryActivities && this._contextCategoryActivities.length > 0) {
      ret.context.contextActivities.category = this._contextCategoryActivities.map(activity => activity.toJson(language, idsOnly))
    }
    if (this._contextGroupingActivities && this._contextGroupingActivities.length > 0) {
      ret.context.contextActivities.grouping = this._contextGroupingActivities.map(activity => activity.toJson(language, idsOnly))
    }
    if (this._contextOtherActivities && this._contextOtherActivities.length > 0) {
      ret.context.contextActivities.other = this._contextOtherActivities.map(activity => activity.toJson(language, idsOnly))
    }
    if (this._contextParentActivities && this._contextParentActivities.length > 0) {
      ret.context.contextActivities.parent = this._contextParentActivities.map(activity => activity.toJson(language, idsOnly))
    }
  }
}
