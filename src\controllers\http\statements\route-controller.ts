import { Router } from 'express'
import putController from './put-statement.controller.js'
import postController from './post-statement.controller.js'
import getController from './get-statement.controller.js'
import multiPartMixedParser from './middleware/multipart-mixed.middleware.js'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import flushController from './flush.controller.js'
import validateQueryString from './middleware/validate-query-string.middleware.js'
import userToAgent from './middleware/userToAgent.middleware.js'

const router = Router()

router.get('/', validateQueryString, getController) // NOSONAR
router.put('/', multiPartMixedParser, userToAgent, putController) // NOSONAR
router.post('/', validateQueryString, multiPartMixedParser, userToAgent, async (req, res) => { // NOSONAR
  if (req.query?.method && typeof req.query.method === 'string') {
    switch (req.query.method.toLowerCase()) {
      case 'put':
        await putController(req, res)
        return
      case 'get':
        await getController(req, res)
        return
      case 'post':
        await postController(req, res)
        return
      default:
        res.status(BAD_REQUEST).send('Unknown method')
        return
    }
  }
  await postController(req, res)
})

// TODO: remove this in production
router.post('/flush', flushController) // NOSONAR

export default router
