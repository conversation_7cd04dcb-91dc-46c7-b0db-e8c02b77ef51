// PUT, update State Resource
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { File } from 'formidable'
import { createHash } from 'crypto'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
import getOrCreateAgentProfileService from '../../../../services/mssql/agent/profile/get-or-create.js'
import AgentProfileModel from '../../../../models/agent-profile.model.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, CONFLICT, INTERNAL_SERVER_ERROR, NO_CONTENT, PRECONDITION_FAILED } = httpStatus
import fsExtra from 'fs-extra'
import parseForm from '../../../../utils/parse-form.js'
import { MISSING_ETAG_INFO, NO_IF_MATCH_ETAG, NO_RESOURCE_MATCH, RESOURCE_DETECTED_ERROR, RESOURCE_DOES_NOT_EXIST, checkModificationConditions } from '../../../../utils/etag.utils.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { EtagPreconditionFail } from '../../../../utils/error.utils.js'
import deleteAgentProfile from '../../../../services/mssql/agent/profile/delete.service.js'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import saveFile from '../../../../services/amqp/file/save.service.js'
import updateAgentProfileService from '../../../../services/mssql/agent/profile/update.service.js'

const log = logger.create('HTTP-Controller.Put-Agent-Profile', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let file: File | undefined
    let base64Contents: string | undefined
    let profile: string | undefined
    const hasher = createHash('sha256')
    let etag: string | undefined

    if (req.is('multipart/*')) {
      // get the file from the request
      const { files } = await parseForm(req)
      let fileCount = 0
      for (const fileKey in files) {
        if (files[fileKey]!.length > 1) {
          fileCount += files[fileKey]!.length
        }
        fileCount++
        file = files[fileKey]![0]
      }
      if (fileCount > 1) {
        // cannot have more than 1 file
        res.status(BAD_REQUEST).send('Invalid form: request cannot contain more than 1 file')
        // remove the temp uploaded files
        for (const fileKey in files) {
          for (const f of files[fileKey]!) {
            await fsExtra.remove(f.filepath)
          }
        }
        return
      }

      if (file) {
        base64Contents = await fsExtra.readFile(file.filepath, 'base64')
        await fsExtra.remove(file.filepath)
        hasher.update(base64Contents)
        etag = hasher.digest('hex')
      }

    } else if (req.is('application/octet-stream')) {
      // payload is a buffer
      profile = (<Buffer>req.body).toString('utf-8')
      hasher.update(profile)
      etag = hasher.digest('hex')

    } else {
      profile = JSON.stringify(req.body)
      hasher.update(profile)
      etag = hasher.digest('hex')
    }

    // get or create the agent
    const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))

    // get or create the activity state record
    const { agentProfile, created } = await getOrCreateAgentProfileService(new AgentProfileModel({
      AgentID: agent.ID,
      FileID: null,
      ProfileID: req.query.profileId!.toString()
    }))

    try {
      checkModificationConditions(req, agentProfile.fields.Etag, created, true)
    } catch (error) {
      if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
        log('warn', 'Failed to create activity state: ETag Precondition failed with resource does not exist. Removing created state record', { success: false, req })
        await deleteAgentProfile(agentProfile.fields.ProfileID!)
        if (agentProfile.fields.FileID) {
          await deleteFile([agentProfile.fields.FileID])
        }
      }
      throw error
    }

    agentProfile.fields.Etag = etag

    if (!created && agentProfile.fields.FileID) {
      // we did not create the record, and it had a file
      // delete the old file
      log('verbose', 'Agent profile had a file previously, removing old file')
      try {
        await deleteFile([agentProfile.fields.FileID])
      } catch (error) {
        // something went wrong the file is now orphaned
        log('error', 'Failed to delete previous agent profile file', { fileId: agentProfile.fields.FileID, errorMessage: getErrorMessage(error), req })
      }
    }

    if (file && base64Contents) {
      // we are updating the file
      agentProfile.fields.JsonProfile = null
      agentProfile.fields.ContentType = file.mimetype ?? 'text/plain'
      log('verbose', 'json profile is file, uploading to FDS')
      agentProfile.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'agent-profile-file.txt', agentProfile.fields.ContentType)
    } else if (profile) {
      agentProfile.fields.JsonProfile = profile
      agentProfile.fields.ContentType = req.headers['content-type'] ?? 'application/json'
    }

    if (req.header('updated') && Date.parse(req.header('updated')!)) {
      agentProfile.fields.Updated = new Date(req.header('updated')!)
    } else {
      agentProfile.fields.Updated = new Date()
    }

    await updateAgentProfileService(agentProfile)
    log('info', 'Successfully updated agent profile', { agentId: agentProfile.fields.AgentID, success: true, req })

    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof EtagPreconditionFail && error.message === MISSING_ETAG_INFO) {
      log('warn', 'Failed to update activity profile: missing etag header', { errorMessage: error.message, success: false, req })
      res.status(BAD_REQUEST).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
      log('warn', 'Failed to update agent profile: resource does not exist', { errorMessage: error.message, success: false, req })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === NO_IF_MATCH_ETAG) {
      log('warn', 'Failed to update agent profile: Etag precondition failed', { errorMessage: error.message, success: false, req })
      res.status(CONFLICT).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DETECTED_ERROR) {
      log('warn', 'Failed to update agent profile: Resource already exists', { errorMessage: error.message, success: false, req })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === NO_RESOURCE_MATCH) {
      log('warn', 'No resources matched your etag precondition', { errorMessage: error.message, success: false, req })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else {
      log('error', 'Failed to save agent profile: unknown error', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}