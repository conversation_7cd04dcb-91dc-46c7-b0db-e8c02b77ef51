import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import VerbModel, { VerbJson } from '../../../models/verb.model.js'
import getVerbDisplay from './display/get.service.js'
import verbCache from '../../cache/verbs.js'
import { Verb, VerbTableName } from '@tess-f/sql-tables/dist/lrs/verb.js'

const log = logger.create('Service-MSSQL.get-verb')

export default async function getVerbService(id: string, getDisplay = true): Promise<VerbModel> {
  const cachedVerb: VerbJson = verbCache.get(id) as VerbJson
  if (cachedVerb) {
    log('info', 'Successfully retrieved verb from cache', { success: true, id })
    // renew the cache
    verbCache.ttl(id)
    log('verbose', 'Successfully renewed verbs time to live in the cache', { success: true })
    return new VerbModel(cachedVerb)
  }

  // verb not found in the cache lets get it from the database if it exists
  const pool = mssql.getPool()
  const verbs = await getRows<Verb>(VerbTableName, pool.request(), { ID: id })
  const verb = new VerbModel(undefined, verbs[0])
  if (getDisplay) {
    try {
      verb.attachDisplay(await getVerbDisplay(id))
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  }
  // set verb in the cache
  verbCache.set(id, verb.fields)
  return verb
}
