import Cache from '@lcs/mq-controlled-cache'
import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../config/settings.js'

const log = logger.create('Cache.Verb-Displays')

const cache = new Cache(
  rabbitMQ,
  settings.caches.exchange_name,
  settings.caches.verbDisplays.cache_label,
  settings.caches.verbDisplays.publish_opts,
  settings.caches.verbDisplays.cache_opts
)

cache.events.off('error', data => log('error', 'Cache error', { data, success: false }))

export default cache
