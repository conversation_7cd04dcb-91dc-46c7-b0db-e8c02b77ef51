import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Controller.Get-About', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // Returns JSON Object containing information about this LRS, including the xAPI version supported.
  // https://github.com/adlnet/xAPI-Spec/blob/master/xAPI-Communication.md#28-about-resource
  const response: any = {
    version: ['1.0.0','1.0.1','1.0.2','1.0.3','2.0.0']
  }

  log('info', 'Successfully returned about resource', { req, success: true })
  res.json(response)
  
}