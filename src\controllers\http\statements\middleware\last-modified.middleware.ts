import { Request, Response, NextFunction } from 'express'

/**
 * Middleware to set Last-Modified header for statement responses
 * According to LRS 2.0 spec section 4.1.6.1:
 * The LRS shall include, in the Last-Modified header, the most recent (maximum) stored property of any of the returned statement(s).
 */
export default function lastModifiedHeader(req: Request, res: Response, next: NextFunction) {
  const originalJson = res.json
  const originalWriteHead = res.writeHead
  res.
  // Helper function to extract max stored time from statements
  function getMaxStoredTime(body: any): Date | null {
    let maxStoredTime: Date | null = null

    // Handle single statement response
    if (body.stored) {
      maxStoredTime = new Date(body.stored)
    }
    // Handle multiple statements response
    else if (body.statements && Array.isArray(body.statements)) {
      for (const statement of body.statements) {
        if (statement.stored) {
          const storedTime = new Date(statement.stored)
          if (!maxStoredTime || storedTime > maxStoredTime) {
            maxStoredTime = storedTime
          }
        }
      }
    }

    return maxStoredTime
  }

  // Helper function to extract max stored time from StatementModel objects
  function getMaxStoredTimeFromModels(statements: any[]): Date | null {
    let maxStoredTime: Date | null = null

    for (const statement of statements) {
      // Handle StatementModel objects with fields property
      const stored = statement.fields?.stored || statement.stored
      if (stored) {
        const storedTime = new Date(stored)
        if (!maxStoredTime || storedTime > maxStoredTime) {
          maxStoredTime = storedTime
        }
      }
    }

    return maxStoredTime
  }

  // Override res.json for JSON responses
  res.json = function(body) {
    if (res.statusCode === 200 && body) {
      const maxStoredTime = getMaxStoredTime(body)
      
      // Set the Last-Modified header if we found a stored timestamp
      if (maxStoredTime && !isNaN(maxStoredTime.getTime())) {
        res.setHeader('Last-Modified', maxStoredTime.toUTCString())
      }
    }

    return originalJson.call(this, body)
  }

  // Override res.writeHead for multipart responses (attachments)
  res.writeHead = function(statusCode: number, headers?: any) {
    // Store the statements data in res.locals for multipart responses
    if (statusCode === 200 && res.locals.statements && Array.isArray(res.locals.statements)) {
      const maxStoredTime = getMaxStoredTimeFromModels(res.locals.statements)

      if (maxStoredTime && !isNaN(maxStoredTime.getTime())) {
        if (!headers) headers = {}
        headers['Last-Modified'] = maxStoredTime.toUTCString()
      }
    }

    return originalWriteHead.call(this, statusCode, headers)
  }

  next()
}