import { Request, Response, NextFunction } from 'express'

/**
 * Middleware to set Last-Modified header for statement responses
 * According to LRS 2.0 spec section 4.1.6.1:
 * The LRS shall include, in the Last-Modified header, the most recent (maximum) stored property of any of the returned statement(s).
 *
 * This middleware provides helper functions to set the Last-Modified header
 * without overriding Express's core functionality.
 */

// Helper function to extract max stored time from JSON response body
function getMaxStoredTime(body: any): Date | null {
  let maxStoredTime: Date | null = null

  // Handle single statement response
  if (body.stored) {
    maxStoredTime = new Date(body.stored)
  }
  // Handle multiple statements response
  else if (body.statements && Array.isArray(body.statements)) {
    for (const statement of body.statements) {
      if (statement.stored) {
        const storedTime = new Date(statement.stored)
        if (!maxStoredTime || storedTime > maxStoredTime) {
          maxStoredTime = storedTime
        }
      }
    }
  }

  return maxStoredTime
}

// Helper function to extract max stored time from StatementModel objects
function getMaxStoredTimeFromModels(statements: any[]): Date | null {
  let maxStoredTime: Date | null = null

  for (const statement of statements) {
    // Handle StatementModel objects with fields property
    const stored = statement.fields?.stored || statement.stored
    if (stored) {
      const storedTime = new Date(stored)
      if (!maxStoredTime || storedTime > maxStoredTime) {
        maxStoredTime = storedTime
      }
    }
  }

  return maxStoredTime
}

/**
 * Sets the Last-Modified header based on statement data in the response body
 * Call this function before sending a JSON response with statement data
 */
export function setLastModifiedFromBody(res: Response, body: any): void {
  if (res.statusCode === 200 && body) {
    const maxStoredTime = getMaxStoredTime(body)

    // Set the Last-Modified header if we found a stored timestamp
    if (maxStoredTime && !isNaN(maxStoredTime.getTime())) {
      res.setHeader('Last-Modified', maxStoredTime.toUTCString())
    }
  }
}

/**
 * Sets the Last-Modified header based on StatementModel objects
 * Call this function before sending multipart responses with statement data
 */
export function setLastModifiedFromModels(res: Response, statements: any[]): void {
  if (res.statusCode === 200 && statements && Array.isArray(statements)) {
    const maxStoredTime = getMaxStoredTimeFromModels(statements)

    if (maxStoredTime && !isNaN(maxStoredTime.getTime())) {
      res.setHeader('Last-Modified', maxStoredTime.toUTCString())
    }
  }
}

/**
 * Legacy middleware function for backward compatibility
 * @deprecated Use setLastModifiedFromBody or setLastModifiedFromModels directly in controllers
 */
export default function lastModifiedHeader(_req: Request, _res: Response, next: NextFunction) {
  // This middleware is now a no-op - the functionality has been moved to explicit helper functions
  // that controllers can call when they have the appropriate data
  next()
}