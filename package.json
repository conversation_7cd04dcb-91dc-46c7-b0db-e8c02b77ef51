{"name": "typescript-express-server-api", "version": "2.0.3", "description": "TypeScript, Express, ES lint, JavaScript Standard Coding Style", "main": "src/index.ts", "exports": "./build/index.js", "type": "module", "scripts": {"start": "node build/index.js", "start:dev": "cross-env CONFIG_PATHS=./tess-config.yaml nodemon --inspect", "build": "rimraf ./build && tsc && npm run copy-files", "copy-files": "copyfiles -u 1 src/**/*.txt src/**/*.json src/**/*.png src/**/*.jpg src/**/*.env src/**/*.ttf build/", "test": "cross-env CONFIG_PATHS=./test-config.yaml ts-mocha \"src/**/*.spec.ts\" --recursive --inspect --exit", "test:watch": "cross-env CONFIG_PATHS=./test-config.yaml ts-mocha src/**/*.spec.ts --inspect -w --watch-files src/**/*.ts", "test:coverage": "nyc --reporter=lcov --reporter=html --reporter=text --reporter=text-summary npm test"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.29.0", "@types/chai": "^5.2.2", "@types/chai-http": "^4.2.4", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/formidable": "^3.4.5", "@types/fs-extra": "^11.0.4", "@types/jws": "^3.2.10", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/mssql": "^9.1.7", "@types/node": "^24.0.3", "@types/prettyjson": "^0.0.33", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "chai": "^5.2.0", "chai-http": "^5.1.2", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^9.29.0", "mocha": "^11.7.1", "mocha-junit-reporter": "^2.2.1", "mocha-multi": "^1.1.7", "node-mocks-http": "^1.17.2", "nodemon": "^3.1.10", "nyc": "^17.1.0", "rimraf": "^6.0.1", "sinon": "^21.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@lcs/logger": "^4.0.3", "@lcs/mq-controlled-cache": "^2.0.0", "@lcs/mssql-utility": "^3.0.0", "@lcs/rabbitmq": "^4.0.2", "@lcs/session-authority": "^4.0.0", "@tess-f/backend-utils": "^2.0.2", "@tess-f/fds": "^2.0.0", "@tess-f/id-mgmt-npm": "^2.0.9", "@tess-f/shared-config": "^2.0.10", "@tess-f/sql-tables": "2.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "express": "^5.1.0", "formidable": "^3.5.4", "fs-extra": "^11.3.0", "http-status": "^2.1.0", "jws": "^4.0.0", "minimist": "^1.2.8", "moment": "^2.30.1", "prettyjson": "^1.2.5", "prom-client": "^15.1.3", "uuid": "^11.1.0", "validate-iri": "^1.0.1"}, "overrides": {"ws": "^8.17.1", "async": "^3.2.6"}, "mocha": {"reporter": "mocha-multi", "reporterOptions": "spec=-,mocha-junit-reporter=-"}}