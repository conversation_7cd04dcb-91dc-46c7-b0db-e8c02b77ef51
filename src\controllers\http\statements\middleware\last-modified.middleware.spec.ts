import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import lastModifiedMiddleware from './last-modified.middleware.js'

describe('HTTP Middleware: Last-Modified Header', () => {
  afterEach(() => Sinon.restore())

  it('should set Last-Modified header for single statement response', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    
    lastModifiedMiddleware(mocks.req, mocks.res, next)
    
    const storedTime = '2023-08-07T10:30:00.000Z'
    const responseBody = {
      id: 'statement-1',
      stored: storedTime,
      actor: { mbox: 'mailto:<EMAIL>' },
      verb: { id: 'http://example.com/verb' },
      object: { id: 'http://example.com/activity' }
    }
    
    mocks.res.json(responseBody)
    
    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.equal(new Date(storedTime).toUTCString())
  })

  it('should set Last-Modified header to most recent stored time for multiple statements', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    
    lastModifiedMiddleware(mocks.req, mocks.res, next)
    
    const olderTime = '2023-08-07T10:30:00.000Z'
    const newerTime = '2023-08-07T11:45:00.000Z'
    const responseBody = {
      statements: [
        {
          id: 'statement-1',
          stored: olderTime,
          actor: { mbox: 'mailto:<EMAIL>' },
          verb: { id: 'http://example.com/verb' },
          object: { id: 'http://example.com/activity' }
        },
        {
          id: 'statement-2',
          stored: newerTime,
          actor: { mbox: 'mailto:<EMAIL>' },
          verb: { id: 'http://example.com/verb' },
          object: { id: 'http://example.com/activity' }
        }
      ]
    }
    
    mocks.res.json(responseBody)
    
    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.equal(new Date(newerTime).toUTCString())
  })

  it('should not set Last-Modified header when no stored timestamp is present', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    
    lastModifiedMiddleware(mocks.req, mocks.res, next)
    
    const responseBody = {
      id: 'statement-1',
      actor: { mbox: 'mailto:<EMAIL>' },
      verb: { id: 'http://example.com/verb' },
      object: { id: 'http://example.com/activity' }
    }
    
    mocks.res.json(responseBody)
    
    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
  })

  it('should not set Last-Modified header for non-200 status codes', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    
    lastModifiedMiddleware(mocks.req, mocks.res, next)
    
    mocks.res.statusCode = 404
    const responseBody = {
      id: 'statement-1',
      stored: '2023-08-07T10:30:00.000Z',
      actor: { mbox: 'mailto:<EMAIL>' },
      verb: { id: 'http://example.com/verb' },
      object: { id: 'http://example.com/activity' }
    }
    
    mocks.res.json(responseBody)
    
    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
  })

  it('should handle empty statements array', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    
    lastModifiedMiddleware(mocks.req, mocks.res, next)
    
    const responseBody = {
      statements: []
    }
    
    mocks.res.json(responseBody)
    
    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
  })

  it('should handle statements without stored timestamps', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    
    lastModifiedMiddleware(mocks.req, mocks.res, next)
    
    const responseBody = {
      statements: [
        {
          id: 'statement-1',
          actor: { mbox: 'mailto:<EMAIL>' },
          verb: { id: 'http://example.com/verb' },
          object: { id: 'http://example.com/activity' }
        }
      ]
    }
    
    mocks.res.json(responseBody)
    
    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
  })

  it('should handle invalid stored timestamps', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()

    lastModifiedMiddleware(mocks.req, mocks.res, next)

    const responseBody = {
      id: 'statement-1',
      stored: 'invalid-date',
      actor: { mbox: 'mailto:<EMAIL>' },
      verb: { id: 'http://example.com/verb' },
      object: { id: 'http://example.com/activity' }
    }

    mocks.res.json(responseBody)

    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
  })

  it('should set Last-Modified header for multipart responses with attachments', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()

    lastModifiedMiddleware(mocks.req, mocks.res, next)

    const storedTime = '2023-08-07T10:30:00.000Z'
    // Simulate storing statements in res.locals as done by the controller
    mocks.res.locals.statements = [
      {
        fields: {
          stored: storedTime
        }
      }
    ]

    // Simulate multipart response
    mocks.res.writeHead(200, { 'content-type': 'multipart/mixed; boundary=test' })

    expect(next.called).to.be.true
    expect(mocks.res.getHeader('Last-Modified')).to.equal(new Date(storedTime).toUTCString())
  })
})
