import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import lastModifiedMiddleware, { setLastModifiedFromBody, setLastModifiedFromModels } from './last-modified.middleware.js'

describe('Last-Modified Header Utilities', () => {
  afterEach(() => Sinon.restore())

  describe('setLastModifiedFromBody', () => {
    it('should set Last-Modified header for single statement response', () => {
      const mocks = httpMocks.createMocks()

      const storedTime = '2023-08-07T10:30:00.000Z'
      const responseBody = {
        id: 'statement-1',
        stored: storedTime,
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'http://example.com/verb' },
        object: { id: 'http://example.com/activity' }
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.equal(new Date(storedTime).toUTCString())
    })

    it('should set Last-Modified header to most recent stored time for multiple statements', () => {
      const mocks = httpMocks.createMocks()

      const olderTime = '2023-08-07T10:30:00.000Z'
      const newerTime = '2023-08-07T11:45:00.000Z'
      const responseBody = {
        statements: [
          {
            id: 'statement-1',
            stored: olderTime,
            actor: { mbox: 'mailto:<EMAIL>' },
            verb: { id: 'http://example.com/verb' },
            object: { id: 'http://example.com/activity' }
          },
          {
            id: 'statement-2',
            stored: newerTime,
            actor: { mbox: 'mailto:<EMAIL>' },
            verb: { id: 'http://example.com/verb' },
            object: { id: 'http://example.com/activity' }
          }
        ]
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.equal(new Date(newerTime).toUTCString())
    })

    it('should not set Last-Modified header when no stored timestamp is present', () => {
      const mocks = httpMocks.createMocks()

      const responseBody = {
        id: 'statement-1',
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'http://example.com/verb' },
        object: { id: 'http://example.com/activity' }
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })

    it('should not set Last-Modified header for non-200 status codes', () => {
      const mocks = httpMocks.createMocks()

      mocks.res.statusCode = 404
      const responseBody = {
        id: 'statement-1',
        stored: '2023-08-07T10:30:00.000Z',
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'http://example.com/verb' },
        object: { id: 'http://example.com/activity' }
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })

    it('should handle empty statements array', () => {
      const mocks = httpMocks.createMocks()

      const responseBody = {
        statements: []
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })

    it('should handle statements without stored timestamps', () => {
      const mocks = httpMocks.createMocks()

      const responseBody = {
        statements: [
          {
            id: 'statement-1',
            actor: { mbox: 'mailto:<EMAIL>' },
            verb: { id: 'http://example.com/verb' },
            object: { id: 'http://example.com/activity' }
          }
        ]
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })

    it('should handle invalid stored timestamps', () => {
      const mocks = httpMocks.createMocks()

      const responseBody = {
        id: 'statement-1',
        stored: 'invalid-date',
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'http://example.com/verb' },
        object: { id: 'http://example.com/activity' }
      }

      setLastModifiedFromBody(mocks.res, responseBody)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })
  })

  describe('setLastModifiedFromModels', () => {
    it('should set Last-Modified header for StatementModel objects', () => {
      const mocks = httpMocks.createMocks()

      const storedTime = '2023-08-07T10:30:00.000Z'
      const statements = [
        {
          fields: {
            stored: storedTime
          }
        }
      ]

      setLastModifiedFromModels(mocks.res, statements)

      expect(mocks.res.getHeader('Last-Modified')).to.equal(new Date(storedTime).toUTCString())
    })

    it('should handle statements without stored property', () => {
      const mocks = httpMocks.createMocks()

      const statements = [
        {
          fields: {
            id: 'statement-1'
          }
        }
      ]

      setLastModifiedFromModels(mocks.res, statements)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })

    it('should not set header for non-200 status codes', () => {
      const mocks = httpMocks.createMocks()
      mocks.res.statusCode = 404

      const storedTime = '2023-08-07T10:30:00.000Z'
      const statements = [
        {
          fields: {
            stored: storedTime
          }
        }
      ]

      setLastModifiedFromModels(mocks.res, statements)

      expect(mocks.res.getHeader('Last-Modified')).to.be.undefined
    })
  })

  describe('legacy middleware', () => {
    it('should be a no-op function', () => {
      const next = Sinon.spy()
      const mocks = httpMocks.createMocks()

      lastModifiedMiddleware(mocks.req, mocks.res, next)

      expect(next.called).to.be.true
    })
  })
})
