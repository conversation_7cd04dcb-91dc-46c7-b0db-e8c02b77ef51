import { Request, Response } from 'express'
import errors from '../../../config/errors.js'
import Statement, { StatementJson, StatementRef } from '../../../models/statement.model.js'
import createStatement from '../../../services/mssql/statements/create.service.js'
import logger from '@lcs/logger'
import validate, { validateVoidStatement } from '../../../services/validators/statement/validate.service.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, CONFLICT, INTERNAL_SERVER_ERROR } = httpStatus
import getStatementById from '../../../services/mssql/statements/get-by-id.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import voidStatement from '../../../services/mssql/statements/void.service.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { VoidStatementError } from '../../../utils/error.utils.js'
import StatementAttachment from '../../../models/attachment.model.js'
import { readFileSync } from 'fs'
import saveFile from '../../../services/amqp/file/save.service.js'
import pkg from 'fs-extra';
const { removeSync } = pkg;
import updateStatementAttachment from '../../../services/mssql/attachments/update.service.js'

const log = logger.create('HTTP-Controller.post-statements', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    validate(req.body)
  } catch (error: unknown) {
    let errorMessage = 'validation error'
    if (error instanceof Error) {
      errorMessage = error.message
    }
    log('warn', 'Failed to save statement(s): validation error', { errorMessage, success: false, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  let statements: Statement[]
  if (Array.isArray(req.body)) {
    statements = req.body.map((value: StatementJson) => {
      if (!value.authority) {
        value.authority = req.authority
      }
      return new Statement(value)
    })
  } else {
    const reqBody = { ...req.body }
    if (!reqBody.authority) {
      reqBody.authority = req.authority
    }
    statements = [new Statement(reqBody)]
  }

  try {
    // check for conflicting statements
    await Promise.all(statements.map(async statement => {
      if (statement.fields.id) {
        // check that there isn't a conflict
        try {
          await getStatementById(statement.fields.id)
          // oh no we found an existing statement
          throw new Error(errors.STATEMENT_CONFLICT)
        } catch (error) {
          // if not found we are good to go
          if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
            throw error
          }
        }
      }

      if (statement.fields.verb?.id.includes('://adlnet.gov/expapi/verbs/voided')) {
        // validate void statement
        await validateVoidStatement((statement.fields.object as StatementRef).id)
      }
    }))

    // we must use a for loop to prevent treading issues
    const createdStatements: Statement[] = []
    for (const s of statements) {
      const statement = await createStatement(s)
      if (statement.fields.verb?.id.includes('://adlnet.gov/expapi/verbs/voided')) {
        // void statements
        const idToVoid = (statement.fields.object as StatementRef).id
        await voidStatement(idToVoid)
        log('verbose', 'Successfully voided statement', { voided: idToVoid, success: true, req })
      }
      createdStatements.push(statement)
    }

    const statementIds = createdStatements.map(statement => statement.fields.id!.toLowerCase())

    log('info', 'Successfully posted statements', { count: createdStatements.length, ids: statementIds, req, success: true })
    res.send(statementIds)

    // process attachments
    if (req.files && req.files.length > 0) {
      // we have files we need to update the records associated with the attachment and store the file in FDS
      try {
        const attachments = createdStatements.reduce((acc, value) => { return [...acc, ...value.attachments] }, new Array<StatementAttachment>)
        for (const file of req.files) {
          // find the attachment
          const attachment = attachments.find(attach => attach.fields.Sha2 === file.hash)
          if (attachment) {
            // save the file in FDS
            try {
              const fileContent = readFileSync(file.filepath, 'base64')
              attachment.fields.FileId = await saveFile(fileContent, file.originalFilename ?? 'statement-attachment', attachment.fields.ContentType!)
              // update the attachment
              await updateStatementAttachment(attachment)
            } catch (error) {
              // silent error
              log('error', 'Failed to save file and update attachment', { success: false, errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), req })
            }
          } else {
            log('warn', 'Failed to save file no matching attachment found', { success: false, req })
          }
          // delete the file now that we are done with it
          removeSync(file.filepath)
        }
      } catch (error) {
        // silent failure
        log('error', 'Failed to process statement attachments', { success: false, errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), req })
      }
    }
  } catch (error) {
    if (error instanceof VoidStatementError) {
      log('warn', 'Failed to post statement there was an invalid voiding statement in the batch', { errorMessage: error.message, success: false, req })
      res.status(BAD_REQUEST).send(error.message)
    } else if (error instanceof Error && error.message === errors.STATEMENT_CONFLICT) {
      log('warn', 'Failed to post statements there was a conflict with one or more of the statements', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(CONFLICT)
    } else {
      log('error', 'Failed to post statements', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
