import express from 'express'
import settings from '../config/settings.js'
import router from './router.js'
import RpcController from '../controllers/amqp/rpc.controller.js'
import rabbitMQ from '@lcs/rabbitmq'
import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { existsSync, mkdirSync } from 'fs'
import path from 'path'
import sessionAuthority from '@lcs/session-authority'

// Caches
import verbCache from '../services/cache/verbs.js'
import verbDisplayCache from '../services/cache/verb-display.js'
import { getErrorMessage } from '@tess-f/backend-utils'

let rpcController: RpcController

const app = express()
app.disable('x-powered-by')
const log = logger.create('Server')
const sessionLog = logger.create('Session-Authority')

export default async function main () {
  try {
    settings.print()
  } catch (error) {
    console.error('Could not print settings', { error })
  }

  try {
    setupLogger()
  } catch (error) {
    console.error('Failed to set up logger', error)
  }

  try {
    await setupMssql()
    await setupRabbit()
    await setupSessionAuthority()
    await setUpCaches()
    await setupControllers()
  } catch (error) {
    console.error('Failed to initialize server', { error })
    throw error
  }

  try {
    if (!existsSync(path.resolve('temp'))) {
      mkdirSync(path.resolve('temp'))
      log('info', 'Created temp upload directory', { success: true })
    }
  } catch (error) {
    throw new Error('Failed to create temp upload directory')
  }

  // Attach the router
  app.use(settings.apiBaseRoute, router)

  // Start the server
  const server = app.listen(settings.server.port, () => log('info', 'Listening on port', { port: settings.server.port, success: true }))

  server.setTimeout(settings.server.timeout)
}

async function setupControllers () {
  rpcController = new RpcController()
  rpcController.connect(settings.amqp.rpcQueue)
  log('info', 'RPC controller connected to queue', { rpcQueue: settings.amqp.rpcQueue, success: true })
}

async function setupRabbit () {
  await rabbitMQ.connect(settings.amqp.config)
  log('info', 'AMQP connection initialized', { success: true })
}

async function setupMssql () {
  await mssql.init(settings.mssql.connectionConfig, settings.mssql.forceEncrypted, settings.mssql.streamChunkSize)
  log('info', 'MSSQL connection initialized', { success: true })
}

function setupLogger () {
  const loggerConfig = JSON.parse(JSON.stringify(settings.logger))
  if (!loggerConfig.useElasticsearch) {
    // if we are not using elasticsearch delete the options
    delete loggerConfig.elasticsearch
  }
  logger.init(loggerConfig)
  log('info', 'Logger initialized', { success: true })
}

async function setUpCaches () {
  await verbCache.init()
  log('info', 'Verb cache initialized', { success: true })
  await verbDisplayCache.init()
  log('info', 'Verb display cache initialized', { success: true })
}

async function setupSessionAuthority () {
  // attach log listeners
  sessionAuthority.events.on('verbose', message => sessionLog('verbose', message.message, { data: message.data }))
  sessionAuthority.events.on('info', message => sessionLog('info', message.message, { data: message.data }))
  sessionAuthority.events.on('error', message => sessionLog('error', message.message, { errorMessage: getErrorMessage(message.error) }))

  // start it up
  try {
    await sessionAuthority.init(settings.sessionAuthority)
  } catch (error) {
    log('error', 'Failed to initialize session authority', { errorMessage: getErrorMessage(error), success: false })
    process.exit(1)
  }
}
