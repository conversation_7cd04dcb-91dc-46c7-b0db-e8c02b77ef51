import Cache from '@lcs/mq-controlled-cache'
import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../config/settings.js'

const log = logger.create('Cache.Verbs')

const cache = new Cache(
  rabbitMQ,
  settings.caches.exchange_name,
  settings.caches.verbs.cache_label,
  settings.caches.verbs.publish_opts,
  settings.caches.verbs.cache_opts
)

cache.events.on('error', data => log('error', 'Cache error', { data, success: false }))

export default cache
