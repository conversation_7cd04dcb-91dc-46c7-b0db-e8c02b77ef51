import { Request, Response } from 'express'
import settings from '../../../config/settings.js'
import { Agent<PERSON>son } from '../../../models/agents.model.js'
import UrlParam from '../../../models/url/url-param.model.js'
import UrlParams from '../../../models/url/url-params.model.js'
import { Url } from '../../../models/url/url.model.js'
import getByIdService from '../../../services/mssql/statements/get-by-id.service.js'
import getBySearchService from '../../../services/mssql/statements/get-by-search.service.js'
import { clamp } from '../../../utils/number.utils.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, OK } = httpStatus
import StatementModel, { StatementJson } from '../../../models/statement.model.js'
import StatementAttachment from '../../../models/attachment.model.js'
import getFile from '../../../services/amqp/file/get.service.js'
import getLanguageFromHeader from '../../../utils/language.js'
import { URL } from 'url'
import { DB_Errors } from '@lcs/mssql-utility'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Controller.Get-Statements', httpLogTransformer)
const CRLF = '\r\n'

export default async function (req: Request, res: Response) {
  // return format, default is exact
  const searchParams = new URL(req.url, 'http://localhost').searchParams
  const formatParam = searchParams.get('format')
  let format = 'exact'
  if (formatParam && ['exact', 'ids', 'canonical'].includes(formatParam)) {
    format = formatParam
  }

  let language: string[] | undefined
  if (format === 'canonical') {
    language = getLanguageFromHeader(req)
  }

  const attachments = searchParams.get('attachments')?.toLowerCase() === 'true'

  if (req.query.statementId || req.query.voidedStatementId) {
    let statementId: string
    if (searchParams.get('statementId')) {
      statementId = searchParams.get('statementId')!
    } else {
      statementId = searchParams.get('voidedStatementId')!
    }

    try {
      const statement = await getByIdService(statementId, attachments, req.query.statementId ? false : true)
      log('info', 'Successfully retrieved statement.', { req, statementId, success: true })
      if (attachments) {
        // Store statement data for Last-Modified header middleware
        res.locals.statements = [statement]
        sendAttachments(res, [statement], format, language, undefined, true)
      } else {
        res.json(statement.toJson(language, format))
      }
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get statement: not found in database', { req, statementId, success: false })
        res.sendStatus(NOT_FOUND)
        return
      }

      log('error', 'Failed to get statement', { req, statementId, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  } else {
    // capture the current query params so we can use them to build a more url
    const params = new UrlParams()
    let agent: AgentJson | undefined
    if (req.query.agent) {
      agent = JSON.parse(req.query.agent.toString())
      params.add(new UrlParam('agent', agent))
    }
    let relatedAgents = false
    if (req.query.related_agents) {
      relatedAgents = Boolean(req.query.related_agents.toString())
      params.add(new UrlParam('related_agents', relatedAgents))
    }
    let verb: string | undefined
    if (req.query.verb) {
      verb = req.query.verb.toString()
      params.add(new UrlParam('verb', verb))
    }
    let activity: string | undefined
    if (req.query.activity) {
      activity = req.query.activity.toString()
      params.add(new UrlParam('activity', activity))
    }
    let relatedActivities = false
    if (req.query.related_activities) {
      relatedActivities = Boolean(req.query.related_activities)
      params.add(new UrlParam('related_activities', relatedActivities))
    }
    let registration: string | undefined
    if (req.query.registration) {
      registration = req.query.registration.toString()
      params.add(new UrlParam('registration', registration))
    }
    let since!: Date
    if (req.query.since && !isNaN(Date.parse(req.query.since.toString()))) {
      since = new Date(req.query.since.toString())
      params.add(new UrlParam('since', since))
    }
    let until!: Date
    if (req.query.until && !isNaN(Date.parse(req.query.until.toString()))) {
      until = new Date(req.query.until.toString())
      params.add(new UrlParam('until', until))
    }
    let limit = 10
    if (req.query.limit) {
      // a limit of zero means get all upto the servers limit
      limit = Number(req.query.limit)
      if (limit === 0) {
        limit = 100
      }
      params.add(new UrlParam('limit', limit))
    }
    limit = clamp(limit, 1, 100)

    let ascending = false
    if (req.query.ascending !== undefined && req.query.ascending !== null && (req.query.ascending.toString() === 'true' || req.query.ascending.toString() === 'false')) {
      ascending = req.query.ascending.toString() === 'true'
    }
    let offset = 0
    if (req.query.offset) {
      offset = Number(req.query.offset?.toString())
    }

    try {
      const statementsResponse = await getBySearchService(
        offset,
        limit,
        agent,
        verb,
        activity,
        registration,
        relatedActivities,
        relatedAgents,
        since,
        until,
        ascending,
        attachments
      )

      const response: { statements: StatementJson[], more?: string } = {
        statements: statementsResponse.statements.map(statement => statement.toJson(language, format))
      }

      // if there are more statements to be had build out the more query
      if (offset + statementsResponse.statements.length < statementsResponse.totalRecords) {
        params.add(new UrlParam('offset', offset + statementsResponse.statements.length))
        params.add(new UrlParam('method', 'GET'))
        const port = settings.server.port !== '80' && settings.server.port !== '443' ? `:${settings.server.port}` : ''
        const url = new Url(`${req.protocol}://${req.hostname}${port}${req.baseUrl}${req.path}`, params)
        response.more = url.toString()
      }

      log('info', 'Successfully fetched statements', { req, count: statementsResponse.statements.length, total: statementsResponse.totalRecords, success: true })

      if (attachments) {
        // Store statement data for Last-Modified header middleware
        res.locals.statements = statementsResponse.statements
        sendAttachments(res, statementsResponse.statements, format, language, response.more, false)
      } else {
        res.json(response)
      }
    } catch (error) {
      log('error', 'Failed to fetch statements', { req, errorMessage: getErrorMessage(error), success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}

async function sendAttachments(res: Response, statements: StatementModel[], format: string, language?: string[], more?: string, single = false) {
  // gather attachments
  const attachments = statements.reduce((acc, statement) => {
    if (statement.attachments) {
      // we only need attachments that have files we stored
      return [...acc, ...statement.attachments.filter(attachment => attachment.fields.FileId)]
    }
    return [...acc]
  }, new Array<StatementAttachment>)

  const attachmentBuffers = await Promise.all(attachments.map(async attachment => {
    // get the file buffer
    const file = await getFile(attachment.fields.FileId!)
    return {
      sha2: attachment.fields.Sha2!,
      payload: file.buffer,
      contentType: attachment.fields.ContentType!
    }
  }))

  const boundary = `=====TESS_LRS_${Date.now()}====`

  res.writeHead(OK, { 'content-type': `multipart/mixed; boundary=${boundary}` })

  // send the statement first
  res.write(`${CRLF}--${boundary}${CRLF}content-type: application/json${CRLF}${CRLF}`)
  if (single) {
    res.write(JSON.stringify(statements[0].toJson(language, format)))
  } else {
    res.write(JSON.stringify({ statements: statements.map(statement => statement.toJson(language, format)), more }))
  }

  // write each attachment
  attachmentBuffers.forEach(attachment => {
    res.write(`${CRLF}--${boundary}${CRLF}content-type:${attachment.contentType}${CRLF}X-Experience-API-Hash:${attachment.sha2}${CRLF}Content-Transfer-Encoding:binary${CRLF}${CRLF}`)
    res.write(attachment.payload)
  })

  // send the end boundary
  res.write(`${CRLF}--${boundary}--${CRLF}`)
  res.end()
}
