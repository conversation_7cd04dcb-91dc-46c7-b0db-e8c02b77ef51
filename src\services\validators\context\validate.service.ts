import { checkAllowed<PERSON>ields, checkIfDict, checkIfList, validate<PERSON><PERSON>, validateUUID } from '../../../utils/validate-data-type.js'
import { validateLanguage } from '../../../utils/validate-language.js'
import validateAgent from '../agent/validate.service.js'
import validateExtensions from '../extensions/validate.service.js'
import { validateActivity, validateStatementRef } from '../object/validate.service.js'

const CONTEXT_ALLOWED_FIELDS = ['registration', 'instructor', 'team', 'contextActivities', 'revision', 'platform', 'language', 'statement', 'extensions', 'contextAgents', 'contextGroups']

export default function validateContext(context: any, statement: any): void {
  // validates context
  // ensure incoming context is a dictionary
  checkIfDict(context, 'Context')
  // check context only contains allowed fields
  checkAllowedFields(CONTEXT_ALLOWED_FIELDS, context, 'Context')

  // If registration included, ensure it is a valid UUID
  if (context.registration) {
    validateUUID(context.registration, 'Context registration')
  }

  // if instructor included, ensure they are valid agent
  if (context.instructor) {
    validateAgent(context.instructor, 'Context instructor')
  }
  // if team is included, ensure they are a valid agent
  if (context.team) {
    validateAgent(context.team, 'Context team')
    if (!context.team.objectType || context.team.objectType === 'Agent') {
      throw new Error('Team in context must be a group')
    }
  }

  // if objectType of object in statement is Agent/Group, context cannot have revision or platform fields
  validateRevisionAndPlatform(context, statement.objectType)

  // if language given, ensure it is a string
  if (context.language) {
    validateContextLanguage(context.language)
  }

  // if statement given, ensure it is a valid StatementRef
  if (context.statement) {
    validateStatementRef(context.statement)
  }

  // if context activities given, ensure they are valid context activities
  if (context.contextActivities) {
    validateContextActivities(context.contextActivities)
  }

  // if context agents given, ensure they are valid
  if (context.contextAgents) {
    validateContextAgents(context.contextAgents)
  }

  // if context groups given, ensure they are valid
  if (context.contextGroups) {
    validateContextGroups(context.contextGroups)
  }

  // if extensions given, ensure they are valid
  if (context.extensions) {
    validateExtensions(context.extensions, 'context extensions')
  }
}

function validateContextLanguage(language: any) {
  if (typeof language !== 'string') {
    throw new Error('Context language must be a string')
  }
  validateLanguage(language, 'context language')
}

function validateRevisionAndPlatform(context: any, statementObjectType: string) {
  if (context.revision) {
    if (typeof context.revision !== 'string') {
      throw new Error('Context revision must be a string')
    }

    if (statementObjectType !== 'Activity') {
      throw new Error('Revision is not allowed in context if statement object is not an Activity')
    }
  }
  if (context.platform) {
    if (typeof context.platform !== 'string') {
      throw new Error('Context platform must be a string')
    }

    if (statementObjectType !== 'Activity') {
      throw new Error('Platform is not allowed in context if statement object is not an Activity')
    }
  }
}

function validateContextActivities(activities: any): void {
  // validate context activities
  // ensure incoming activities is a dictionary
  checkIfDict(activities, 'Context activity')
  const activityTypes = ['parent', 'grouping', 'category', 'other']
  for (const key in activities) {
    // check if activity is a valid type
    if (!activityTypes.includes(key)) {
      throw new Error(`Context activity type is not valid - ${key} - must be ${activityTypes.join(', ')}`)
    }

    // ensure activity is a list or dictionary
    if (Array.isArray(activities[key])) {
      activities[key].forEach((act: any) => {
        validateActivity(act)
      })
    } else if (typeof activities[key] === 'object') {
      validateActivity(activities[key])
      // Convert context activities to list if dict (requirement)
      activities[key] = [activities[key]]
    } else {
      throw new Error('contextActivities is not formatted correctly')
    }
  }
}

function validateContextAgents(agents: any): void {
  // validates context agents
  checkIfList(agents, 'Context Agents')

  agents.forEach((agent: any) => {
    if (!agent.objectType) {
      throw new Error('[objectType] Context Agent entries must have an objectType')
    }
    // Validate that objectType is 'contextAgent'
    if (agent.objectType !== 'contextAgent') {
      throw new Error(`[objectType] Context Agent entries must be 'contextAgent', got ${agent.objectType}`)
    }

    if (!agent.relevantTypes) {
      throw new Error('[relevantTypes] Context Agent entries must have relevantTypes')
    }

    if (!Array.isArray(agent.relevantTypes) || agent.relevantTypes.length <= 0) {
      throw new Error('[relevantTypes] Context Agent entries must be a non-empty list')
    }

    // validate that all elements in relevantTypes are valid IRIs
    agent.relevantTypes.forEach((relevantType: any) => {
      validateIri(relevantType, 'relevantTypes')
    })

    // Validate the agent object
    if (!agent.agent) {
      throw new Error('[agent] Context Agent entries must have an agent')
    }

    validateAgent(agent.agent, 'Context agent')
  })
}

function validateContextGroups(groups: any): void {
  // validates a contexts groups
  checkIfList(groups, 'Context Groups')

  groups.forEach((group: any) => {
    if (!group.objectType) {
      throw new Error('[objectType] Context Group entries must have an objectType')
    }

    if (group.objectType !== 'contextGroup') {
      throw new Error(`[objectType] Context Group entries must be 'contextGroup', got ${group.objectType}`)
    }

    // validate relevantTypes is a list
    if (!group.relevantTypes) {
      throw new Error('[relevantTypes] Context Group entries must have relevantTypes')
    }

    if (!Array.isArray(group.relevantTypes) || group.relevantTypes.length <= 0) {
      throw new Error('[relevantTypes] Context Group entries must be a non-empty list')
    }

    // validate that all elements in relevantTypes are valid IRIs
    group.relevantTypes.forEach((relevantType: any) => {
      validateIri(relevantType, 'relevantTypes')
    })

    if (!group.group) {
      throw new Error('[group] Context Group entries must have a group')
    }

    // validate the group object
    validateAgent(group.group, 'Context group')
  })
}
